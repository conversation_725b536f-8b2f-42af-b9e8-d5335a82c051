import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AddPaymentForApprovalRoutingModule } from './add-payment-for-approval-routing.module';
import { AddPaymentForApprovalComponent } from './components/add-payment-for-approval/add-payment-for-approval.component';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCDatepickerModule } from '@maids/cc-lib/date';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { CCIconModule } from '@maids/cc-lib/icon';
import { DuplicatedDialogComponent } from './components/duplicated-dialog/duplicated-dialog.component';
import { CCRadioButtonModule } from '@maids/cc-lib/radio-button';
import { CCAmountInputModule } from "@maids/cc-lib/masked-input";
import { ExistingPaymentsDialogComponent } from './components/existing-payments-dialog/existing-payments-dialog.component';
import {CCBreadcrumbsModule} from "@maids/cc-lib/layout";


@NgModule({
  declarations: [
    AddPaymentForApprovalComponent,
    DuplicatedDialogComponent,
    ExistingPaymentsDialogComponent
  ],
    imports: [
        CommonModule,
        AddPaymentForApprovalRoutingModule,
        ReactiveFormsModule,
        FormsModule,
        CCInputModule,
        CCSelectInputModule,
        CCDatagridModule,
        CCDialogModule,
        CCCheckboxModule,
        CCButtonModule,
        CCDatepickerModule,
        CCTextareaModule,
        CCFileUploaderModule.forChild({}),
        CCIconModule,
        CCRadioButtonModule,
        CCAmountInputModule,
        CCBreadcrumbsModule
    ]
})
export class AddPaymentForApprovalModule { }
