import { Component, Inject } from '@angular/core';
import { NavigationItem } from '@maids/cc-lib/layout';
import { CCThemeService, CC_AVAILABLE_THEMES } from '@maids/cc-lib/theme';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'microfrontend-accounting',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent {
  title = 'accounting';
  links: NavigationItem = {
    type: 'main',
    children: [
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Manage Revenues',
            navLink: '/revenues',
            hidden: false,
            active: true,
          },
          // {
          //   type: 'link',
          //   text: 'Confirm Page',
          //   navLink: 'accounting/confirm-dd/849668',
          //   hidden: false,
          //   active: true,
          // },
        ],
      },
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Manage Buckets',
            navLink: '/manage-buckets',
            hidden: false,
            active: true,
          },
        ],
      },
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Collection events config',
            navLink: '/collection-events-config',
            hidden: false,
            active: true,
          },
        ],
      },
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Multiple DD Configuration',
            navLink: '/multiple-dd-configuration',
            hidden: false,
            active: true,
          },
        ],
      },
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Telecom Management',
            navLink: '/telecom-management',
            hidden: false,
            active: true,
          },
        ],
      },
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Maid Expenses Summary',
            navLink: 'maid-expenses-summary',
            hidden: false,
            active: true,
          },
        ],
      },
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Deposits',
            navLink: '/deposits',
            hidden: false,
            active: true,
          },
        ],
      },
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Audit Manager',
            navLink: 'audit-manager',
            hidden: false,
            active: true,
          },
        ],
      },
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Failed DDs Generation',
            navLink: '/failed-dd-generation',
            hidden: false,
            active: true,
          },
        ],
      },
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Payment Plans Data Entry',
            navLink: '/contracts-info',
            hidden: false,
            active: true,
          },
        ],
      },
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Cashier',
            navLink: '/cashier',
            active: true,
            hidden: false,
          },
        ],
      },
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Pay Invoices',
            navLink: '/pay-invoices',
            hidden: false,
            active: true,
          },
        ],
      },
      {
        type: 'menu',
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {
            type: 'link',
            text: 'Transaction Posting Setup',
            navLink: '/transactions-posting-engine',
            hidden: false,
            active: true,
          },
        ],
      },
      {
        type: "menu",
        text:`${this.title}`,
        icon:'book',
        expanded:false,
        hidden:false,
        children:[
          {type: "link", text:"Statement of Account", navLink:'/statement-of-account',hidden:false,active:true},
        ]
      },
      {
        type: "menu",
        text: `${this.title}`,
        icon: 'book',
        expanded: false,
        hidden: false,
        children: [
          {type: "link", text: "Risk Documents Management", navLink: '/risk-documents-mgmt', hidden: false, active: true},
        ]
      }
    ],
  };
  localBuild = !environment.production && environment.newErp;
  env = {...environment, newErp: true}
  constructor(
    @Inject(CC_AVAILABLE_THEMES) public readonly themes: CC_AVAILABLE_THEMES,
    private _themeService: CCThemeService
  ) {}
}
