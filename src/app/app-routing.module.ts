import { APP_BASE_HREF } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CCAuthGuard } from '@maids/cc-erp-services';
import { LoginComponent } from './modules/core/components/login/login.component';
import { CooControlRoutingModule } from './modules/coo-control/coo-control-routing.module';

import { EmptyRouteComponent } from './empty-route/empty-route.component';
import { environment } from 'src/environments/environment';

const routes: Routes = [
  {
    path: 'accounting/v2',
    children: [
      {
        path: 'suppliers-list',
        loadChildren: () =>
          import('./modules/suppliers/suppliers.module').then(
            (m) => m.SuppliersModule
          ),
        data: { label: 'Suppliers List', pageCode: 'accounting_SupplierList' },
      },
      {
        path: 'risk-documents-mgmt',
        loadChildren: () =>
          import(
            './modules/risk-management-documents/risk-management-documents.module'
          ).then((m) => m.RiskManagementDocumentsModule),
        canActivate: [CCAuthGuard],
        data: {
          label: 'Risk Documents Management Screen',
          pageCode: 'riskDocumentsMgmt',
        },
      },
      {
        path: 'pnl-report',
        loadChildren: () =>
          import('./modules/pnl-report/pnl-report.module').then(
            (m) => m.PnlReportModule
          ),
        canActivate: [CCAuthGuard],
        canLoad: [CCAuthGuard],
        data: {
          label: 'PnL Report Generation',
          pageCode: 'accounting_pnl_report',
        },
      },
      {
        path: 'transactions-posting-engine',
        loadChildren: () =>
          import(
            './modules/transaction-posting-setup/transaction-posting-setup.module'
          ).then((m) => m.TransactionPostingSetupModule),
        data: {
          label: 'Transaction Posting Rules',
          pageCode: 'accounting_posting-engine',
        },
      },
      {
        path: 'revenues',
        loadChildren: () =>
          import('./modules/manage-revenues/manage-revenues.module').then(
            (m) => m.ManageRevenuesModule
          ),
        data: { label: 'Revenues', pageCode: 'Revenues' },
      },
      {
        path: 'manage-buckets',
        loadChildren: () =>
          import('./modules/manage-buckets/manage-buckets.module').then(
            (m) => m.ManageBucketsModule
          ),
        data: { label: 'Manage Buckets', pageCode: 'ManageBuckets' },
      },
      {
        path: 'collection-events-config',
        loadChildren: () =>
          import(
            './modules/collection-events-config/collection-events-config.module'
          ).then((m) => m.CollectionEventsConfigModule),
        data: {
          label: 'Collection Events Config',
          pageCode: 'ACCOUNTING__CollectionEventsConfig',
        },
      },
      {
        path: 'multiple-dd-configuration',
        loadChildren: () =>
          import(
            './modules/multiple-dd-configuration/multiple-dd-configuration.module'
          ).then((m) => m.MultipleDdConfigurationModule),
        data: {
          label: 'Multiple DD Configuration',
          pageCode: 'multiple-dd-configuration',
        },
      },
      {
        path: 'telecom-management',
        loadChildren: () =>
          import('./modules/telecom-management/telecom-management.module').then(
            (m) => m.TelecomManagementModule
          ),
        data: { label: 'Telecom Management', pageCode: 'TelecomManagement' },
      },
      {
        path: 'maid-expenses-summary',
        loadChildren: () =>
          import(
            './modules/maid-expenses-summary/maid-expenses-summary.module'
          ).then((m) => m.MaidExpensesSummaryModule),
        data: {
          label: 'Maid Expenses Summary',
          pageCode: 'ACCOUNTING__MaidExpensesSummary',
        },
      },
      {
        path: 'deposits',
        loadChildren: () =>
          import('./modules/deposits/deposits.module').then(
            (m) => m.DepositsModule
          ),
        data: { label: 'Deposits', pageCode: 'accounting_deposits' },
      },
      {
        path: 'audit-manager',
        loadChildren: () =>
          import('./modules/audit-manager/audit-manager.module').then(
            (m) => m.AuditManagerModule
          ),
        data: { label: 'Audit Manager', pageCode: 'accounting_AuditManager' },
      },
      {
        path: 'failed-dd-generation',
        loadChildren: () =>
          import(
            './modules/failed-dd-generation/failed-dd-generation.module'
          ).then((m) => m.FailedDdGenerationModule),
        data: {
          label: 'Failed DD Generation',
          pageCode: 'ACCOUNTING__FailedDDsGeneration',
        },
      },
      {
        path: 'contracts-info',
        loadChildren: () =>
          import(
            './modules/payment-plans-and-discounts/payment-plans-and-discounts.module'
          ).then((m) => m.PaymentPlansAndDiscountsModule),
        data: {
          label: 'Payment Plans and Discounts',
          pageCode: 'ACCOUNTING__PaymentPlansDataEntry',
        },
      },
      {
        path: 'cashier',
        loadChildren: () =>
          import('./modules/cashier/cashier.module').then(
            (m) => m.CashierModule
          ),
        data: { label: 'Cashier', pageCode: 'ACCOUNTING__CashierScreen' },
      },
      {
        path: 'pay-invoices',
        loadChildren: () =>
          import('./modules/pay-invoices/pay-invoices.module').then(
            (m) => m.PayInvoicesModule
          ),
        data: { label: 'Pay Invoices', pageCode: 'accounting_PayInvoice' },
      },
      {
        path: 'payments-automation/direct-debit-applications',
        loadChildren: () =>
          import(
            '../app/modules/direct-debit-applications/direct-debit-applications.module'
          ).then((m) => m.DirectDebitApplicationsModule),
        data: {
          label: 'Direct Debit Applications',
          pageCode: 'accounting_direct-debit-applications',
        },
      },
      {
        path: 'contract-payments-files/:id',
        loadChildren: () =>
          import(
            '../app/modules/contract-payments-files/contract-payments-files.module'
          ).then((m) => m.ContractPaymentsFilesModule),
        data: { label: 'Contract Payments Files' },
      },
      {
        path: 'expense-category',
        loadChildren: () =>
          import(
            '../app/modules/expense-category/expense-category.module'
          ).then((m) => m.ExpenseCategoryModule),
        data: {
          label: 'Expense Management',
          pageCode: 'accounting_ExpenseCategory',
        },
      },
      {
        path: 'expense-items',
        loadChildren: () =>
          import('./modules/expense-items/expense-items.module').then(
            (m) => m.ExpenseItemsModule
          ),
        data: {
          label: 'Expense Management',
          pageCode: 'accounting_ExpenseItems',
        },
      },
      {
        path: 'tenancy-contracts',
        loadChildren: () =>
          import('./modules/tenancy-contracts/tenancy-contracts.module').then(
            (m) => m.TenancyContractsModule
          ),
        data: {
          label: "Company's Contract & Agreements",
          pageCode: "Company'sContractAndAgreementsDetails",
        },
      },
      {
        path: 'pdc-list',
        loadChildren: () =>
          import('./modules/pdc/pdc.module').then((m) => m.PdcModule),
        data: { label: 'PDC Management', pageCode: 'pdc-management' },
      },
      {
        path: 'expenses-refunds-history',
        loadChildren: () =>
          import(
            '../app/modules/expenses-refunds-history/expenses-refunds-history.module'
          ).then((m) => m.ExpensesRefundsHistoryModule),
        data: {
          label: 'Expenses refund history',
          pageCode: 'ExpenseRefundsHistory',
        },
      },
      {
        path: 'threshold-table',
        loadChildren: () =>
          import('./modules/threshold-table/threshold-table.module').then(
            (m) => m.ThresholdTableModule
          ),
        data: { label: 'Purpose', pageCode: 'accounting_threshold_table' },
      },
      {
        path: 'companies',
        loadChildren: () =>
          import('./modules/companies/companies.module').then(
            (m) => m.CompaniesModule
          ),
        data: { label: 'Companies', pageCode: 'companies' },
      },
      {
        path: 'statement-of-account',
        loadChildren: () =>
          import(
            './modules/statement-of-account/statement-of-account.module'
          ).then((m) => m.StatementOfAccountModule),
        canActivate: [CCAuthGuard],
        canLoad: [CCAuthGuard],
        data: { label: 'Statement Of Account', pageCode: 'statementOfAccount' },
      },
      {
        path: 'bank-dd-cancellation-file',
        loadChildren: () =>
          import(
            './modules/bank-dd-cancellation-file/bank-dd-cancellation-file.module'
          ).then((m) => m.BankDdCancellationFileModule),
        data: {
          label: 'Bank Direct Debit Cancellation File',
          pageCode: 'accounting_bank-direct-debit-cancellation-file',
        },
      },
      {
        path: 'client-refund-setup',
        loadChildren: () =>
          import(
            './modules/client-refund-setup/client-refund-setup.module'
          ).then((m) => m.ClientRefundSetupModule),
        data: {
          label: 'Family Refund Setup',
          pageCode: 'accounting_ClientRefundSetup',
        },
      },
      {
        path: 'cancel-dd-from-bank',
        loadChildren: () =>
          import(
            './modules/cancel-dd-from-bank/cancel-dd-from-bank.module'
          ).then((m) => m.CancelDdFromBankModule),
        data: {
          label: 'Cancel future DDs from bank',
          pageCode: 'accounting_cancelFutrueDdsFromBank',
        },
      },
      {
        path: 'send-manual-dd-to-bank',
        loadChildren: () =>
          import(
            './modules/send-manual-dd-to-bank/send-manual-dd-to-bank.module'
          ).then((m) => m.SendManualDdToBankModule),
        data: {
          label: 'Send Confirmed Manual DD to the bank',
          pageCode: 'send-manual-dd-to-bank',
        },
      },
      {
        path: 'purchase-requests',
        loadChildren: () =>
          import('./modules/purchase-requests/purchase-requests.module').then(
            (m) => m.PurchaseRequestsModule
          ),
        data: {
          label: 'Purchase Auditor',
          pageCode: 'ACCOUNTING__PurchaseRequests',
        },
      },
      {
        path: 'unknown-wire-transfer',
        loadChildren: () =>
          import(
            './modules/unknown-wire-transfer/unknown-wire-transfer.module'
          ).then((m) => m.UnknownWireTransferModule),
        data: {
          label: 'Unknown Wire Transfer',
          pageCode: 'UnknownWireTransfer',
        },
      },
      {
        path: 'insurance-agreements',
        loadChildren: () =>
          import(
            './modules/insurance-agreements/insurance-agreements.module'
          ).then((m) => m.InsuranceAgreementsModule),
        data: {
          label: 'Insurance Agreements',
          pageCode: 'accounting_insurance_agreements',
        },
      },
      {
        path: 'credit-card-holder',
        loadChildren: () =>
          import('./modules/credit-card-holder/credit-card-holder.module').then(
            (m) => m.CreditCardHolderModule
          ),
        data: {
          label: 'Credit Card Holder',
          pageCode: 'accounting_CreditCardHolder',
        },
      },
      {
        path: 'dewa-report',
        loadChildren: () =>
          import('./modules/dewa-report/dewa-report.module').then(
            (m) => m.DewaReportModule
          ),
        data: { label: 'DEWA Report', pageCode: 'DewaReport' },
      },
      {
        path: 'payments-automation/send-dd-to-bank',
        loadChildren: () =>
          import('./modules/send-dd-to-bank/send-dd-to-bank.module').then(
            (m) => m.SendDdToBankModule
          ),
        data: {
          label: 'Send DD to Bank',
          pageCode: 'accounting_send-dd-to-bank',
        },
      },
      {
        path: 'manage-transactions',
        loadChildren: () =>
          import(
            './modules/manage-transactions/manage-transactions.module'
          ).then((m) => m.ManageTransactionsModule),
        data: { label: 'Manage Transactions', pageCode: 'ManageTransactions' },
      },
      {
        path: 'payments-automation/dd-data-entry',
        loadChildren: () =>
          import('./modules/dd-data-entry/dd-data-entry.module').then(
            (m) => m.DdDataEntryModule
          ),
        data: { label: 'DD Data Entry', pageCode: 'accounting_dd-data-entry' },
      },
      {
        path: 'payments-automation/contract-payments',
        loadChildren: () =>
          import('./modules/contract-payments/contract-payments.module').then(
            (m) => m.ContractPaymentsModule
          ),
        data: {
          label: 'Contract Payments Confirmation',
          pageCode: 'accounting_contract-payments',
        },
      },
      {
        path: 'pnl-vs-system-code',
        loadChildren: () =>
          import('./modules/pnl-vs-system-code/pnl-vs-system-code.module').then(
            (m) => m.PnlVsSystemCodeModule
          ),
        data: {
          label: 'P&L VS System Code',
          pageCode: 'accounting_pnl_vs_system_code',
        },
      },
      {
        path: 'payments-automation/import-new-file/ddFile',
        loadChildren: () =>
          import(
            './modules/bank-dd-activation-file/bank-dd-activation-file.module'
          ).then((m) => m.BankDdActivationFileModule),
        data: {
          label: 'Bank Direct Debit Activation File',
          pageCode: 'accounting_automation-import-new-file',
        },
      },
      {
        path: 'upload-statements',
        loadChildren: () =>
          import('./modules/upload-statement/upload-statement.module').then(
            (m) => m.UploadStatementModule
          ),
        data: {
          label: 'Upload Statements',
          pageCode: 'accounting_UploadStatements',
        },
      },
      {
        path: 'visa-expenses',
        loadChildren: () =>
          import('./modules/visa-expenses/visa-expenses.module').then(
            (m) => m.VisaExpensesModule
          ),
        data: { label: 'Visa Expenses', pageCode: 'VisaExpenses' },
      },
      {
        path: 'request-expense',
        loadChildren: () =>
          import('./modules/request-expense/request-expense.module').then(
            (m) => m.RequestExpenseModule
          ),
        data: {
          label: 'Request Expense',
          pageCode: 'ACCOUNTING__RequestExpense',
        },
      },
      {
        path: 'stock-keeper-todo',
        loadChildren: () =>
          import('./modules/stock-keeper-todo/stock-keeper-todo.module').then(
            (m) => m.StockKeeperTodoModule
          ),
        data: {
          label: 'Stock Keeper Todo',
          pageCode: 'accounting_StockKeeperTodo',
        },
      },
      {
        path: 'purchase-manager',
        loadChildren: () =>
          import('./modules/purchasing-manager/purchasing-manager.module').then(
            (m) => m.PurchasingManagerModule
          ),
        data: {
          label: 'Purchase Manager Todo',
          pageCode: 'ACCOUNTING__PurchasingManager',
        },
      },
      {
        path: 'payment-request-flow/add-maid-refund',
        loadChildren: () =>
          import('./modules/add-maid-refund/add-maid-refund.module').then(
            (m) => m.AddMaidRefundModule
          ),
        data: {
          label: 'Add Payment for Maid',
          pageCode: 'accounting_add_client_refund',
        },
      },
      {
        path: 'payment-request-flow/add-client-refund',
        loadChildren: () =>
          import('./modules/add-client-refund/add-client-refund.module').then(
            (m) => m.AddClientRefundModule
          ),
        data: {
          label: 'Add Family Refund',
          pageCode: 'accounting_add_client_refund',
        },
      },
      {
        path: 'recurring-cc-payments-issues-mgmt',
        loadChildren: () =>
          import(
            './modules/recurring-cc-payments-issues-mgmt/recurring-cc-payments-issues-mgmt.module'
          ).then((m) => m.RecurringCcPaymentsIssuesMgmtModule),
        data: {
          label: 'Recurring CC Payments Issues',
          pageCode: 'ACCOUNTING__recurringCCPaymentsIssuesMgmt',
        },
      },
      {
        path: 'insurance-invoicing',
        loadChildren: () =>
          import(
            './modules/insurance-invoicing/insurance-invoicing.module'
          ).then((m) => m.InsuranceInvoicingModule),
        data: {
          label: 'Insurance Invoicing',
          pageCode: 'accounting_insurance_invoicing',
        },
      },
      {
        path: 'insurance-auditing',
        loadChildren: () =>
          import('./modules/insurance-auditing/insurance-auditing.module').then(
            (m) => m.InsuranceAuditingModule
          ),
        data: {
          label: 'Insurance Auditing',
          pageCode: 'ACCOUNTING__InsuranceAuditing',
        },
      },
      {
        path: 'purchase-order-history',
        loadChildren: () =>
          import(
            './modules/purchase-order-history/purchase-order-history.module'
          ).then((m) => m.PurchaseOrderHistoryModule),
        data: {
          label: 'Purchase Order History',
          pageCode: 'ACCOUNTING__PurchaseOrderHistory',
        },
      },
      {
        path: 'reconciliator',
        loadChildren: () =>
          import('./modules/reconciliator/reconciliator.module').then(
            (m) => m.ReconciliatorModule
          ),
        data: { label: 'Reconciliator', pageCode: 'ACCOUNTING__Reconciliator' },
      },
      {
        path: 'accountant-todo',
        loadChildren: () =>
          import('./modules/accountant-todo/accountant-todo.module').then(
            (m) => m.AccountantTodoModule
          ),
        data: {
          label: 'Bank transfers',
          pageCode: 'accounting_AccountantTodoList',
        },
      },
      {
        path: 'payment-report',
        loadChildren: () =>
          import('./modules/payment-report/payment-report.module').then(
            (m) => m.PaymentReportModule
          ),
        data: { label: 'Payments Records Report', pageCode: 'PaymentReport' },
      },
      {
        path: 'expenses-approvals',
        loadChildren: () =>
          import('./modules/expenses-approvals/expenses-approvals.module').then(
            (m) => m.ExpensesApprovalsModule
          ),
        data: {
          label: 'Expense Approval',
          pageCode: 'ACCOUNTING__ExpensesApprovals',
        },
      },
      {
        path: 'expenses',
        loadChildren: () =>
          import('./modules/expenses/expenses.module').then(
            (m) => m.ExpensesModule
          ),
        data: { label: 'Manage Expenses', pageCode: 'Expenses' },
      },
      {
        path: 'bank-transfer-confirmation',
        loadChildren: () =>
          import(
            './modules/bank-transfer-confirmation/bank-transfer-confirmation.module'
          ).then((m) => m.BankTransferConfirmationModule),
        data: {
          label: 'Bank Transfer Confirmation',
          pageCode: 'accounting_BankTransferConfirmation',
        },
      },
      {
        path: 'expenses-requests',
        loadChildren: () =>
          import('./modules/expense-requests/expense-requests.module').then(
            (m) => m.ExpenseRequestsModule
          ),
        data: {
          label: 'Expenses Requests History',
          pageCode: 'ACCOUNTING__ExpensesRequests',
        },
      },
      {
        path: 'coo-questions/answer/:id',
        loadChildren: () =>
          import(
            './modules/answer-coo-question/answer-coo-question.module'
          ).then((m) => m.AnswerCooQuestionModule),
        data: {
          label: 'Question from COO',
          pageCode: 'ACCOUNTING__PurchaseOrderHistory',
        },
      },
      {
        path: 'payments/add-for-approval/:id/:contractId',
        loadChildren: () =>
          import(
            './modules/add-payment-for-approval/add-payment-for-approval.module'
          ).then((m) => m.AddPaymentForApprovalModule),
        data: {
          pageCode: 'AddClientPaymentsForApproval',
        },
      },
      {
        path: 'recurring-cc-payments-issues-mgmt',
        loadChildren: () =>
          import(
            './modules/recurring-cc-payments-issues-mgmt/recurring-cc-payments-issues-mgmt.module'
          ).then((m) => m.RecurringCcPaymentsIssuesMgmtModule),
        data: { label: 'Reccurring CC Payments Issues Management Screen' },
      },
      {
        path: 'direct-debit-form/:contractId',
        loadChildren: () =>
          import('./modules/direct-debit-form/direct-debit-form.module').then(
            (m) => m.DirectDebitFormModule
          ),
        data: {
          pageCode: 'accountingDirectDebitForm',
        },
      },
      {
        path: 'client-refund-summary',
        data: {
          label: 'Family Refund Summary',
          pageCode: 'accounting_client-refund-summary',
        },
        loadChildren: () =>
          import(
            './modules/client-refund-summary/client-refund-summary.module'
          ).then((m) => m.ClientRefundSummaryModule),
      },
      {
        path: 'client-refund-approvals',
        data: {
          label: 'Family Refund Approvals',
          pageCode: 'accounting_ClientRefundApprovals',
        },
        loadChildren: () =>
          import(
            './modules/family-refund-approvals/family-refund-approvals.module'
          ).then((m) => m.FamilyRefundApprovalsModule),
      },
      {
        path: 'dd-messaging-setup',
        loadChildren: () =>
          import('./modules/dd-messaging-setup/dd-messaging-setup.module').then(
            (m) => m.DdMessagingSetupModule
          ),
        data: { label: 'DD Messaging Setup' },
      },
      {
        path: 'contract-dd-payments/:id',
        loadChildren: () =>
          import(
            './modules/contract-dd-paymets/contract-dd-paymets.module'
          ).then((m) => m.ContractDdPaymetsModule),
        data: {
          label: 'Direct Debit Payments',
          pageCode: 'accountingDirectDebitPayments',
        },
      },
      {
        path: 'coo-control',
        loadChildren: () =>
          import('./modules/coo-control/coo-control.module').then(
            (m) => m.CooControlModule
          ),
        data: { label: 'COO Control', pageCode: 'ACCOUNTING__COOControl' },
      },
      {
        path: 'pnlreport-structure',
        loadChildren: () =>
          import(
            './modules/pnlreport-structure/pnlreport-structure.module'
          ).then((m) => m.PnlreportStructureModule),
        data: {
          label: 'PnL Report Structure',
          pageCode: 'pnlreport_structure',
        },
      },
      {
        path: 'client-refund-approvals',
        data: {
          label: 'Family Refund Approvals',
          pageCode: 'accounting_ClientRefundApprovals',
        },
        loadChildren: () =>
          import(
            './modules/family-refund-approvals/family-refund-approvals.module'
          ).then((m) => m.FamilyRefundApprovalsModule),
      },
      {
        path: 'request-expense',
        loadChildren: () =>
          import('./modules/request-expense/request-expense.module').then(
            (m) => m.RequestExpenseModule
          ),
        data: {
          label: 'Request Expense',
          pageCode: 'ACCOUNTING__RequestExpense',
        },
      },
      {
        path: 'cashier',
        loadChildren: () =>
          import('./modules/cashier/cashier.module').then(
            (m) => m.CashierModule
          ),
        data: { label: 'Cashier', pageCode: 'ACCOUNTING__CashierScreen' },
      },
      {
        path: 'suppliers-list',
        loadChildren: () =>
          import('./modules/suppliers/suppliers.module').then(
            (m) => m.SuppliersModule
          ),
        data: { label: 'Suppliers List', pageCode: 'accounting_SupplierList' },
      },
      {
        path: 'manage-buckets',
        loadChildren: () =>
          import('./modules/manage-buckets/manage-buckets.module').then(
            (m) => m.ManageBucketsModule
          ),
        data: { label: 'Manage Buckets', pageCode: 'ManageBuckets' },
      },
      {
        path: 'reconciliator',
        loadChildren: () =>
          import('./modules/reconciliator/reconciliator.module').then(
            (m) => m.ReconciliatorModule
          ),
        data: { label: 'Reconciliator', pageCode: 'ACCOUNTING__Reconciliator' },
      },
      {
        path: 'bank-dd-cancellation-file',
        loadChildren: () =>
          import(
            './modules/bank-dd-cancellation-file/bank-dd-cancellation-file.module'
          ).then((m) => m.BankDdCancellationFileModule),
        data: {
          label: 'Bank Direct Debit Cancellation File',
          pageCode: 'accounting_bank-direct-debit-cancellation-file',
        },
      },
      {
        path: 'visa-expenses',
        loadChildren: () =>
          import('./modules/visa-expenses/visa-expenses.module').then(
            (m) => m.VisaExpensesModule
          ),
        data: { label: 'Visa Expenses', pageCode: 'VisaExpenses' },
      },
      {
        path: 'payments-automation/direct-debit-applications',
        loadChildren: () =>
          import(
            '../app/modules/direct-debit-applications/direct-debit-applications.module'
          ).then((m) => m.DirectDebitApplicationsModule),
        data: {
          label: 'Direct Debit Applications',
          pageCode: 'accounting_direct-debit-applications',
        },
      },
      {
        path: 'dd-messaging-setup',
        loadChildren: () =>
          import('./modules/dd-messaging-setup/dd-messaging-setup.module').then(
            (m) => m.DdMessagingSetupModule
          ),
        data: {
          label: 'DD Messaging Setup',
          pageCode: 'accounting_ddMessagingSetup',
        },
      },
    ],
    data: { label: 'Accounting', disabled: true },
  },
  { path: 'login', component: LoginComponent },
  { path: '**', component: EmptyRouteComponent },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      onSameUrlNavigation: 'reload',
      useHash: !environment.production && !environment.newErp,
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
